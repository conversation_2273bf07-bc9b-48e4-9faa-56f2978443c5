import { prisma } from '@/lib/prisma';
import * as bcrypt from 'bcrypt';

/**
 * Service for managing bot users in the system
 * Handles creation, authentication, and management of automated bot users
 */
export class BotUserService {
  private static readonly BOT_USER_ID = 3; // As referenced in socket-server.ts
  private static readonly BOT_ROLE_ID = 4; // Bot role from seed.ts

  /**
   * Get or create the system bot user
   * This bot user is used for automated message sending
   */
  static async getOrCreateSystemBot() {
    try {
      // First, try to find existing bot user
      let botUser = await prisma.user.findUnique({
        where: { id: this.BOT_USER_ID },
        include: {
          userRole: true,
        },
      });

      if (botUser) {
        console.log('Found existing bot user:', botUser.id);
        return botUser;
      }

      // Create bot user if it doesn't exist
      console.log('Creating new system bot user...');
      
      // Generate a secure password for the bot (though it won't be used for login)
      const botPassword = await bcrypt.hash(`bot-${Date.now()}-${Math.random()}`, 10);

      botUser = await prisma.user.create({
        data: {
          id: this.BOT_USER_ID,
          email: '<EMAIL>',
          passwordHash: botPassword,
          firstName: 'System',
          lastName: 'Bot',
          phone: null,
          imageUrl: null,
          isBot: true,
          userRoleId: this.BOT_ROLE_ID,
        },
        include: {
          userRole: true,
        },
      });

      console.log('Created system bot user:', botUser.id);
      return botUser;
    } catch (error) {
      console.error('Error getting or creating system bot:', error);
      throw new Error(`Failed to get or create system bot: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Ensure bot user is a participant in a specific chat
   * This is required for the bot to send messages to the chat
   */
  static async ensureBotInChat(chatId: number) {
    try {
      const botUser = await this.getOrCreateSystemBot();

      // Check if bot is already in the chat
      const existingChatUser = await prisma.chatUser.findFirst({
        where: {
          chatId: chatId,
          userId: botUser.id,
        },
      });

      if (existingChatUser) {
        return existingChatUser;
      }

      // Add bot to the chat
      const chatUser = await prisma.chatUser.create({
        data: {
          chatId: chatId,
          userId: botUser.id,
          isAdmin: false, // Bot is not an admin
        },
      });

      console.log(`Added bot user to chat ${chatId}`);
      return chatUser;
    } catch (error) {
      console.error(`Error ensuring bot in chat ${chatId}:`, error);
      throw new Error(`Failed to add bot to chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Remove bot from a specific chat
   */
  static async removeBotFromChat(chatId: number) {
    try {
      const botUser = await this.getOrCreateSystemBot();

      const result = await prisma.chatUser.deleteMany({
        where: {
          chatId: chatId,
          userId: botUser.id,
        },
      });

      console.log(`Removed bot user from chat ${chatId}, deleted ${result.count} records`);
      return result;
    } catch (error) {
      console.error(`Error removing bot from chat ${chatId}:`, error);
      throw new Error(`Failed to remove bot from chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get all chats where the bot is a participant
   */
  static async getBotChats() {
    try {
      const botUser = await this.getOrCreateSystemBot();

      const chatUsers = await prisma.chatUser.findMany({
        where: {
          userId: botUser.id,
        },
        include: {
          chat: {
            include: {
              organization: {
                select: { id: true, name: true },
              },
              department: {
                select: { id: true, name: true },
              },
              task: {
                select: { id: true, taskTitle: true },
              },
            },
          },
        },
      });

      return chatUsers.map(cu => cu.chat);
    } catch (error) {
      console.error('Error getting bot chats:', error);
      throw new Error(`Failed to get bot chats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if a user is a bot
   */
  static async isBot(userId: number): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { isBot: true, userRoleId: true },
      });

      return user?.isBot === true || user?.userRoleId === this.BOT_ROLE_ID;
    } catch (error) {
      console.error(`Error checking if user ${userId} is bot:`, error);
      return false;
    }
  }

  /**
   * Get bot user information
   */
  static async getBotUser() {
    try {
      return await this.getOrCreateSystemBot();
    } catch (error) {
      console.error('Error getting bot user:', error);
      throw error;
    }
  }
}

export default BotUserService;
